"""
FastAPI Backend for MCP Client with Bedrock Integration - FIXED VERSION
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from contextlib import asynccontextmanager, AsyncExitStack
import uvicorn
import boto3
from botocore.config import Config
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import os
from dotenv import load_dotenv
import uuid
import anyio


load_dotenv()



try:
    from jsonschema import validate, Draft202012Validator, ValidationError
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

# Simple telemetry tracking
class TelemetryTracker:
    """Simple telemetry tracker for performance monitoring"""
    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "total_tool_calls": 0,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_latency_ms": 0,
            "error_count": 0,
            "tool_execution_times": [],
            "conversation_turns": 0
        }

    def record_conversation_turn(self, usage: Dict, metrics: Dict, tool_count: int = 0):
        """Record metrics from a conversation turn"""
        self.metrics["total_requests"] += 1
        self.metrics["conversation_turns"] += 1
        self.metrics["total_tool_calls"] += tool_count
        self.metrics["total_input_tokens"] += usage.get("inputTokens", 0)
        self.metrics["total_output_tokens"] += usage.get("outputTokens", 0)
        self.metrics["total_latency_ms"] += metrics.get("latencyMs", 0)

    def record_error(self):
        """Record an error occurrence"""
        self.metrics["error_count"] += 1

    def record_tool_execution_time(self, execution_time_ms: float):
        """Record tool execution time"""
        self.metrics["tool_execution_times"].append(execution_time_ms)

    def get_summary(self) -> Dict:
        """Get telemetry summary"""
        avg_latency = (self.metrics["total_latency_ms"] / max(1, self.metrics["total_requests"]))
        avg_tool_time = (sum(self.metrics["tool_execution_times"]) /
                        max(1, len(self.metrics["tool_execution_times"])))

        return {
            **self.metrics,
            "average_latency_ms": avg_latency,
            "average_tool_execution_ms": avg_tool_time,
            "success_rate": 1 - (self.metrics["error_count"] / max(1, self.metrics["total_requests"]))
        }

# Global telemetry tracker
telemetry = TelemetryTracker()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic Models
class MCPServerConfig(BaseModel):
    name: str
    command: str
    args: List[str] = []
    env: Dict[str, str] = {}
    description: str = ""
    enabled: bool = True

class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    use_tools: bool = True

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"

class MCPServerConnection:
    """Manages individual MCP server connection"""
    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.tools = []
        self.resources = []
        self.status = "disconnected"
        self.error = None
        self.tool_registry = {}  # Optimized tool lookup: name -> {description, input_schema}
        
    async def connect(self):
        """Connect to the MCP server with robust lifecycle management and AnyIO-safe cleanup."""
        # Always use a fresh AsyncExitStack per connect attempt to avoid cross-task exits
        self.exit_stack = AsyncExitStack()
        self.session = None
        self.status = "connecting"
        self.error = None



        try:
            logger.info(f"Connecting to MCP server: {self.config.name}")
            logger.info(f"Command: {self.config.command}")
            logger.info(f"Args: {self.config.args}")
            logger.info(f"Env: {self.config.env}")

            # 1) Build server params
            server_params = StdioServerParameters(
                command=self.config.command,
                args=self.config.args,
                env=self.config.env
            )
            logger.info(f"Server params created for {self.config.name}")

            # 2) Enter stdio_client WITHOUT asyncio.wait_for (avoid cancel-scope mismatch)
            logger.info(f"Creating stdio_client for {self.config.name}")
            transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
            read, write = transport
            logger.info(f"stdio_client created successfully for {self.config.name}")

            # 3) Enter ClientSession WITHOUT asyncio.wait_for (same reason)
            logger.info(f"Creating ClientSession for {self.config.name}")
            self.session = await self.exit_stack.enter_async_context(ClientSession(read, write))
            logger.info(f"ClientSession created successfully for {self.config.name}")

            # 4) Initialize the session WITH a timeout (safe to cancel this call)
            #    Higher timeout for slow servers and first-time runs
            init_timeout = 90.0 if self.config.name in ("cost-explorer", "aws-pricing") else 60.0
            try:
                self.status = "initializing"
                await asyncio.wait_for(self.session.initialize(), timeout=init_timeout)
                logger.info(f"Session initialized for {self.config.name}")
            except asyncio.TimeoutError:
                self.status = "error"
                self.error = f"Session initialization timeout ({init_timeout}s)"
                logger.error(f"Session initialization timeout for {self.config.name}")
                # Shield cleanup from cancellation/anyio scope issues
                try:
                    import anyio
                    with anyio.CancelScope(shield=True):
                        await asyncio.shield(self.exit_stack.aclose())
                except Exception as ce:
                    logger.warning(f"Cleanup error after init timeout for {self.config.name}: {ce}")
                return False

            # 5) List tools/resources with bounded timeouts; cancellation here is OK
            try:
                await asyncio.wait_for(self._list_tools(), timeout=30.0)
                await asyncio.wait_for(self._list_resources(), timeout=30.0)
            except asyncio.TimeoutError:
                logger.warning(f"Tool/resource listing timeout for {self.config.name}, proceeding anyway")

            self.status = "connected"
            self.error = None
            logger.info(f"Successfully connected to MCP server: {self.config.name} with {len(self.tools)} tools")
            return True

        except Exception as e:
            self.status = "error"
            self.error = str(e)
            logger.error(f"Failed to connect to MCP server {self.config.name}: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Exception details: {repr(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            # Final shielded cleanup (same task as entered)
            try:
                import anyio
                with anyio.CancelScope(shield=True):
                    await asyncio.shield(self.exit_stack.aclose())
            except Exception as ce:
                logger.warning(f"Cleanup error for {self.config.name}: {ce}")
            return False
    
    async def _list_tools(self):
        """List tools from the server and build optimized registry"""
        try:
            if not self.session:
                return

            tools_result = await self.session.list_tools()
            self.tools = []
            self.tool_registry = {}  # Reset registry

            if hasattr(tools_result, 'tools') and tools_result.tools:
                for tool in tools_result.tools:
                    # Extract input schema properly
                    input_schema = {}
                    if hasattr(tool, 'inputSchema'):
                        input_schema = tool.inputSchema
                    elif hasattr(tool, 'input_schema'):
                        input_schema = tool.input_schema

                    tool_info = {
                        "name": tool.name,
                        "description": getattr(tool, 'description', ''),
                        "input_schema": input_schema
                    }
                    self.tools.append(tool_info)

                    # Build optimized registry for O(1) lookup
                    self.tool_registry[tool.name] = {
                        "description": getattr(tool, 'description', ''),
                        "input_schema": input_schema,
                        "server": self.config.name
                    }

                    logger.info(f"Found tool: {tool.name} on server {self.config.name}")

        except Exception as e:
            logger.error(f"Error listing tools for {self.config.name}: {e}")
    
    async def _list_resources(self):
        """List resources from the server"""
        try:
            if not self.session:
                return
                
            resources_result = await self.session.list_resources()
            self.resources = []
            
            if hasattr(resources_result, 'resources') and resources_result.resources:
                self.resources = [
                    {
                        "uri": resource.uri,
                        "name": getattr(resource, 'name', ''),
                        "description": getattr(resource, 'description', ''),
                        "mimeType": getattr(resource, 'mimeType', '')
                    }
                    for resource in resources_result.resources
                ]
                logger.info(f"Found {len(self.resources)} resources on server {self.config.name}")
                
        except Exception as e:
            logger.error(f"Error listing resources for {self.config.name}: {e}")

    def validate_tool_arguments(self, tool_name: str, arguments: Dict[str, Any]) -> tuple[Dict[str, Any], List[str]]:
        """Validate tool arguments against JSON schema and return validated args + warnings"""
        warnings = []

        # Get tool info from registry (O(1) lookup)
        tool_info = self.tool_registry.get(tool_name)
        if not tool_info:
            warnings.append(f"Tool {tool_name} not found in registry")
            return arguments, warnings

        schema = tool_info.get("input_schema", {})
        if not schema or not JSONSCHEMA_AVAILABLE:
            # Basic validation - check required fields only
            if "required" in schema:
                for req_field in schema["required"]:
                    if req_field not in arguments:
                        warnings.append(f"Missing required field: {req_field}")
            return arguments, warnings

        try:
            # Full JSON schema validation
            Draft202012Validator.check_schema(schema)
            validate(instance=arguments, schema=schema)
            return arguments, warnings
        except ValidationError as e:
            warnings.append(f"Schema validation failed: {e.message}")
            return arguments, warnings
        except Exception as e:
            warnings.append(f"Validation error: {str(e)}")
            return arguments, warnings

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on this server with enhanced validation"""
        try:
            if not self.session or self.status != "connected":
                return {
                    "success": False,
                    "error": f"Server {self.config.name} is not connected (status: {self.status})",
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }

            # Check tool exists using optimized registry
            if tool_name not in self.tool_registry:
                available_tools = list(self.tool_registry.keys())
                return {
                    "success": False,
                    "error": f"Tool {tool_name} not found on server {self.config.name}. Available: {available_tools}",
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }

            # Validate arguments with enhanced schema validation
            validated_args, validation_warnings = self.validate_tool_arguments(tool_name, arguments)

            if validation_warnings:
                # Log warnings but continue with execution
                logger.warning(f"Tool {tool_name} validation warnings: {validation_warnings}")

                # If there are critical validation errors, return error
                critical_errors = [w for w in validation_warnings if "Missing required field" in w or "Schema validation failed" in w]
                if critical_errors:
                    return {
                        "success": False,
                        "error": f"Validation failed: {'; '.join(critical_errors)}",
                        "tool_name": tool_name,
                        "server_name": self.config.name
                    }

            logger.info(f"Calling tool {tool_name} on {self.config.name} with validated arguments: {validated_args}")

            # Call the tool with validated arguments
            result = await self.session.call_tool(tool_name, validated_args)

            # Handle different result formats
            if hasattr(result, 'content'):
                content = result.content
                if isinstance(content, list):
                    # Extract text from content list
                    text_parts = []
                    for item in content:
                        if hasattr(item, 'text'):
                            text_parts.append(item.text)
                        elif isinstance(item, dict) and 'text' in item:
                            text_parts.append(item['text'])
                        else:
                            text_parts.append(str(item))
                    content = '\n'.join(text_parts) if text_parts else str(content)

                logger.info(f"Tool {tool_name} returned: {content}")
                return {
                    "success": True,
                    "result": content,
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }
            else:
                result_str = str(result)
                logger.info(f"Tool {tool_name} returned: {result_str}")
                return {
                    "success": True,
                    "result": result_str,
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }

        except Exception as e:
            logger.error(f"Tool execution error for {tool_name} on {self.config.name}: {e}")
            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}",
                "tool_name": tool_name,
                "server_name": self.config.name
            }
    
    async def disconnect(self):
        """Disconnect from the server using AsyncExitStack for safe cleanup"""
        try:
            logger.info(f"Starting disconnect for {self.config.name}")
            # AsyncExitStack handles all cleanup automatically and safely
            await self.exit_stack.aclose()
            self.session = None
            self.status = "disconnected"
            logger.info(f"Successfully disconnected from {self.config.name}")

        except Exception as e:
            logger.error(f"Error during disconnect from {self.config.name}: {e}")
            # Force cleanup even if there were errors
            self.session = None
            self.status = "error"


class MCPClientManager:
    def __init__(self):
        self.connections: Dict[str, MCPServerConnection] = {}
        self.bedrock_client = None
        self.global_tool_registry = {}  # Global registry: tool_name -> {server, schema, description}
        self._initialize_bedrock()
    
    def _initialize_bedrock(self):
        """Initialize AWS Bedrock client"""
        try:
            # Configure with retry settings for better reliability
            config = Config(
                region_name=os.getenv('AWS_REGION', 'ap-south-1'),
                retries={'max_attempts': 3, 'mode': 'adaptive'}
            )
            
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                config=config,
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
            )
            logger.info("Bedrock client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    def _rebuild_global_tool_registry(self):
        """Rebuild the global tool registry from all connected servers"""
        self.global_tool_registry = {}
        for server_name, connection in self.connections.items():
            if connection.status == "connected":
                for tool_name, tool_info in connection.tool_registry.items():
                    # Use server:tool_name as key to handle name conflicts
                    global_key = f"{server_name}::{tool_name}"
                    self.global_tool_registry[global_key] = {
                        "server": server_name,
                        "tool_name": tool_name,
                        "description": tool_info["description"],
                        "input_schema": tool_info["input_schema"]
                    }
        logger.info(f"Global tool registry rebuilt with {len(self.global_tool_registry)} tools")

    async def add_server(self, config: MCPServerConfig) -> bool:
        """Add and connect to an MCP server"""
        # Create connection object
        connection = MCPServerConnection(config)
        self.connections[config.name] = connection

        # Attempt to connect
        success = await connection.connect()

        # Rebuild global registry if connection successful
        if success:
            self._rebuild_global_tool_registry()

        return success

    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools using optimized global registry"""
        tools = {}
        for tool_key, tool_info in self.global_tool_registry.items():
            server_name = tool_info["server"]
            connection = self.connections.get(server_name)

            # Only include tools from connected servers
            if connection and connection.status == "connected":
                tools[tool_key] = {
                    "server": server_name,
                    "tool": {
                        "name": tool_info["tool_name"],
                        "description": tool_info["description"],
                        "input_schema": tool_info["input_schema"]
                    },
                    "connection": connection
                }
        return tools

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a specific MCP server"""
        if server_name not in self.connections:
            return {
                "success": False,
                "error": f"Server {server_name} not found",
                "tool_name": tool_name,
                "server_name": server_name
            }
        
        connection = self.connections[server_name]
        
        if connection.status != "connected":
            # Try to reconnect
            logger.info(f"Attempting to reconnect to {server_name}")
            success = await connection.connect()
            if not success:
                return {
                    "success": False,
                    "error": f"Server {server_name} is not connected and reconnection failed: {connection.error}",
                    "tool_name": tool_name,
                    "server_name": server_name
                }
        
        return await connection.call_tool(tool_name, arguments)

    async def chat_with_bedrock(self, message: str, tools_available: List[str] = None) -> str:
        """Chat with Bedrock with fixed multiple tool call handling"""
        try:
            model_id = os.getenv('BEDROCK_MODEL_ID')
            print(model_id)
            # Get current available tools
            available_tools = self.get_available_tools()
            
            system_message = "You are a helpful AI assistant with access to various tools."
            if available_tools:
                tools_info = "\n\nAvailable tools:\n"
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    tools_info += f"- {tool['name']} (server: {tool_data['server']}): {tool['description']}\n"
                system_message += tools_info

            messages = [{
                "role": "user",
                "content": [{"text": message}]
            }]

            inference_config = {
                "maxTokens": 4000,
                "temperature": 0.7,
                "topP": 0.9
            }

            system = [{"text": system_message}]

            tool_config = None
            if tools_available and available_tools:
                tools = []
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    
                    # Ensure proper schema format for Bedrock
                    input_schema = tool.get("input_schema", {})
                    if not input_schema:
                        input_schema = {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    
                    # Make sure the schema has the required structure
                    if "type" not in input_schema:
                        input_schema["type"] = "object"
                    if "properties" not in input_schema:
                        input_schema["properties"] = {}
                    
                    tools.append({
                        "toolSpec": {
                            "name": tool["name"],
                            "description": tool["description"] or f"Tool from server {tool_data['server']}",
                            "inputSchema": {
                                "json": input_schema
                            }
                        }
                    })
                
                tool_config = {
                    "tools": tools,
                    "toolChoice": {"auto": {}}
                }
                
                logger.info(f"Configured {len(tools)} tools for Bedrock")

            # Keep conversing until we get an end turn
            max_iterations = 10  # Prevent infinite loops
            iteration = 0
            total_tool_count = 0  # Track total tools used across all iterations
            
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"Bedrock conversation iteration {iteration}")
                
                converse_params = {
                    "modelId": model_id,
                    "messages": messages,
                    "system": system,
                    "inferenceConfig": inference_config
                }

                if tool_config:
                    converse_params["toolConfig"] = tool_config

                response = self.bedrock_client.converse(**converse_params)
                stop_reason = response.get('stopReason')

                # Capture usage and metrics for observability
                usage = response.get('usage', {})
                metrics = response.get('metrics', {})

                logger.info(f"Stop reason: {stop_reason}")
                if usage:
                    logger.info(f"Token usage - Input: {usage.get('inputTokens', 0)}, Output: {usage.get('outputTokens', 0)}")
                if metrics:
                    logger.info(f"Latency: {metrics.get('latencyMs', 0)}ms")

                # Record telemetry for this turn
                iteration_tool_count = 0

                # Handle tool use response with parallel execution
                if stop_reason == 'tool_use':
                    assistant_message = response.get('output', {}).get('message', {})
                    messages.append(assistant_message)

                    # Collect all tool use requests for parallel execution
                    tool_requests = []
                    for content in assistant_message.get('content', []):
                        if content.get('toolUse'):
                            tool_use = content['toolUse']
                            tool_name = tool_use.get('name')
                            tool_input = tool_use.get('input', {})
                            tool_use_id = tool_use.get('toolUseId')

                            logger.info(f"Tool use requested: {tool_name} with input: {tool_input}")

                            # Find the server for this tool
                            server_name = None
                            for tool_key, tool_data in available_tools.items():
                                if tool_data["tool"]["name"] == tool_name:
                                    server_name = tool_data["server"]
                                    break

                            tool_requests.append({
                                "tool_use_id": tool_use_id,
                                "tool_name": tool_name,
                                "tool_input": tool_input,
                                "server_name": server_name
                            })

                    # Execute all tools concurrently with asyncio.gather
                    logger.info(f"Executing {len(tool_requests)} tools in parallel")
                    iteration_tool_count = len(tool_requests)
                    total_tool_count += iteration_tool_count

                    # Record start time for tool execution telemetry
                    import time
                    tool_start_time = time.time() * 1000  # Convert to milliseconds

                    async def execute_single_tool(request):
                        """Execute a single tool and return formatted result as schema-compliant toolResult payload."""
                        tool_use_id = request["tool_use_id"]
                        tool_name = request["tool_name"]
                        tool_input = request["tool_input"]
                        server_name = request["server_name"]

                        if server_name:
                            try:
                                result = await self.call_tool(server_name, tool_name, tool_input)
                                # Build a single json content for toolResult with success + result/error
                                if result["success"]:
                                    result_data = result.get("result", "")
                                    # Try to return structured JSON if possible, else text via json wrapper
                                    try:
                                        if isinstance(result_data, (dict, list)):
                                            payload = {"success": True, "result": result_data}
                                        elif isinstance(result_data, str):
                                            try:
                                                parsed = json.loads(result_data)
                                                payload = {"success": True, "result": parsed}
                                            except (json.JSONDecodeError, ValueError):
                                                payload = {"success": True, "result": result_data}
                                        else:
                                            payload = {"success": True, "result": str(result_data)}
                                    except Exception:
                                        payload = {"success": True, "result": str(result_data)}

                                    return {
                                        "toolUseId": tool_use_id,
                                        "content": [{"json": payload}]
                                    }
                                else:
                                    payload = {"success": False, "error": result.get("error", "Unknown error")}
                                    return {
                                        "toolUseId": tool_use_id,
                                        "content": [{"json": payload}]
                                    }
                            except Exception as e:
                                return {
                                    "toolUseId": tool_use_id,
                                    "content": [{"json": {"success": False, "error": f"Execution error: {str(e)}"}}]
                                }
                        else:
                            return {
                                "toolUseId": tool_use_id,
                                "content": [{"json": {"success": False, "error": f"Tool {tool_name} not found"}}]
                            }

                    # Run all tools concurrently
                    tool_results = await asyncio.gather(*[
                        execute_single_tool(request) for request in tool_requests
                    ], return_exceptions=True)

                    # Handle any exceptions from gather
                    processed_results = []
                    for i, result in enumerate(tool_results):
                        if isinstance(result, Exception):
                            logger.error(f"Tool execution exception: {result}")
                            # Preserve as text content in follow-up message; no 'status' sibling (schema compliance)
                            processed_results.append({
                                "toolUseId": tool_requests[i]["tool_use_id"],
                                "content": [{"text": f"Execution exception: {str(result)}"}]
                            })
                        else:
                            processed_results.append(result)

                    tool_results = processed_results

                    # Record tool execution time
                    tool_end_time = time.time() * 1000
                    tool_execution_time = tool_end_time - tool_start_time
                    telemetry.record_tool_execution_time(tool_execution_time)
                    logger.info(f"Tool execution completed in {tool_execution_time:.2f}ms")

                    # Build toolResult message only with allowed fields
                    if tool_results:
                        tool_result_message = {"role": "user", "content": []}
                        for tool_result in tool_results:
                            tool_result_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_result["toolUseId"],
                                    "content": tool_result["content"]
                                }
                            })
                        messages.append(tool_result_message)
                        continue
                    
                elif stop_reason in ['end_turn', 'stop_sequence', 'max_tokens']:
                    # This is the final response - capture final metrics
                    final_usage = response.get('usage', {})
                    final_metrics = response.get('metrics', {})

                    # Record final telemetry
                    telemetry.record_conversation_turn(final_usage, final_metrics, total_tool_count)

                    logger.info(f"Conversation completed. Final usage: {final_usage}, Final metrics: {final_metrics}")

                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            return {
                                "response": content['text'],
                                "tools_used": total_tool_count,
                                "status": "success"
                            }

                    # If no text content found, return a default message
                    return {
                        "response": 'Response completed.',
                        "tools_used": total_tool_count,
                        "status": "success"
                    }

                else:
                    # Unexpected stop reason, handle as final response
                    logger.warning(f"Unexpected stop reason: {stop_reason}")
                    unexpected_usage = response.get('usage', {})
                    unexpected_metrics = response.get('metrics', {})
                    logger.info(f"Unexpected termination usage: {unexpected_usage}, metrics: {unexpected_metrics}")

                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            return {
                                "response": content['text'],
                                "tools_used": total_tool_count,
                                "status": "success"
                            }
                    return {
                        "response": f'Response completed with stop reason: {stop_reason}',
                        "tools_used": total_tool_count,
                        "status": "success"
                    }

            # If we hit max iterations
            return {
                "response": 'Response completed after maximum iterations.',
                "tools_used": total_tool_count,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Error in Bedrock chat: {e}")
            return {
                "response": f"Error: {str(e)}",
                "tools_used": total_tool_count if 'total_tool_count' in locals() else 0,
                "status": "error"
            }

    async def cleanup(self):
        """Clean up all connections - Fixed scope issue"""
        logger.info("Starting cleanup of all connections")
        
        # Create a list to avoid modifying dict during iteration
        connections_to_cleanup = list(self.connections.items())
        
        # Cleanup each connection with proper error handling
        for name, connection in connections_to_cleanup:
            try:
                logger.info(f"Cleaning up connection: {name}")
                await connection.disconnect()
            except Exception as e:
                logger.error(f"Error cleaning up connection {name}: {e}")
        
        # Clear the connections dict
        self.connections.clear()
        logger.info("Cleanup completed")


# Helper function to build UV tool executable paths
def get_uv_tool_executable_path(tool_name: str) -> str:
    """Get the full path to a UV tool executable"""
    uv_tools_dir = os.path.expanduser("~\\AppData\\Roaming\\uv\\tools")
    return os.path.join(uv_tools_dir, tool_name, "Scripts", f"awslabs.{tool_name.replace('awslabs-', '').replace('-', '-')}.exe")

# Default server configurations using uv tool run format
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cloudformation",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.cfn-mcp-server@latest",
            "awslabs.cfn-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        description="AWS CloudFormation MCP Server",
        enabled=True
    ),
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.cost-explorer-mcp-server@latest",
            "awslabs.cost-explorer-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server",
        enabled=True
    ),
    MCPServerConfig(
        name="aws-pricing",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs.aws-pricing-mcp-server@latest",
            "awslabs.aws-pricing-mcp-server.exe"
        ],
        env={
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Pricing MCP Server",
        enabled=True
    )
]

# Global MCP client manager
mcp_manager = MCPClientManager()

# Modified lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with auto-server setup"""
    # Startup
    logger.info("Starting MCP Client API")

    # Auto-configure default servers (non-blocking)
    if os.getenv("AUTO_CONFIGURE_SERVERS", "false").lower() == "true":
        logger.info("Auto-configuring default MCP servers...")

        # Use asyncio.gather with return_exceptions=True to prevent one failure from stopping others
        async def configure_server_safe(server_config):
            """Configure a server with error handling and retry logic"""
            if not server_config.enabled:
                return False, f"Server {server_config.name} is disabled"

            logger.info(f"Adding server: {server_config.name}")

            # Retry logic for timeout-prone servers
            max_retries = 2 if server_config.name in ["cost-explorer", "aws-pricing"] else 1

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                        await asyncio.sleep(2.0)  # Wait before retry

                    success = await mcp_manager.add_server(server_config)
                    if success:
                        logger.info(f"âœ… Successfully configured {server_config.name}")
                        return True, None
                    else:
                        connection = mcp_manager.connections.get(server_config.name)
                        error_msg = connection.error if connection else "Unknown error"

                        # Don't retry if it's not a timeout error
                        if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                            logger.error(f"âŒ Failed to configure {server_config.name}: {error_msg}")
                            return False, error_msg
                        else:
                            logger.warning(f"âš ï¸  Timeout for {server_config.name}, will retry...")

                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"âŒ Error configuring {server_config.name}: {e}")
                        return False, str(e)
                    else:
                        logger.warning(f"âš ï¸  Exception for {server_config.name}, will retry: {e}")

            return False, "Max retries exceeded"

        # Configure servers sequentially to reduce resource contention
        # This helps with timeout issues during concurrent startup
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            result = await configure_server_safe(config)
            results.append(result)
            # Small delay between server startups
            await asyncio.sleep(1.0)

        # Count successful connections
        successful_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"âŒ Exception configuring {DEFAULT_MCP_SERVERS[i].name}: {result}")
            elif result[0]:  # success
                successful_count += 1

        logger.info(f"ðŸš€ Startup complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")

        # Don't fail startup even if no servers connect
        if successful_count == 0:
            logger.warning("âš ï¸  No MCP servers connected, but API will still start")

    try:
        yield
    finally:
        # Shutdown
        logger.info("Shutting down MCP Client API")
        try:
            await mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Create FastAPI app
app = FastAPI(
    title="MCP Client API",
    description="Multi-server MCP client with Bedrock integration",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Endpoints
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "MCP Client API is running", "status": "healthy"}

@app.get("/servers")
async def list_servers():
    """List all configured MCP servers"""
    servers_info = {}
    for name, connection in mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info

@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server"""
    success = await mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")

@app.get("/servers/{server_name}")
async def get_server_details(server_name: str):
    """Get detailed information about a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    return {
        "name": server_name,
        "status": connection.status,
        "config": connection.config.model_dump(),
        "tools": connection.tools,
        "resources": connection.resources,
        "error": connection.error
    }

@app.get("/tools")
async def list_tools():
    """List all available tools across all servers"""
    tools = {}
    available_tools = mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools

@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool"""
    result = await mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint with tool integration"""
    try:
        # Get conversation ID (generate if not provided)
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        
        # Use tools if requested and available
        available_tools = mcp_manager.get_available_tools()
        tools_available = list(available_tools.keys()) if request.use_tools else []
        
        # Get response from Bedrock
        result = await mcp_manager.chat_with_bedrock(
            request.message,
            tools_available
        )

        # Handle both old string format and new dict format for backward compatibility
        if isinstance(result, dict):
            response_text = result.get("response", "")
            tools_used_count = result.get("tools_used", 0)
            status = result.get("status", "success")
        else:
            # Backward compatibility for string responses
            response_text = str(result)
            tools_used_count = 0
            status = "success"

        return ChatResponse(
            response=response_text,
            conversation_id=conversation_id,
            tools_used=[f"tool_{i}" for i in range(tools_used_count)],  # Create dummy tool list for count
            status=status
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Additional endpoint for reconnecting to a server
@app.post("/servers/{server_name}/reconnect")
async def reconnect_server(server_name: str):
    """Reconnect to a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    
    # Disconnect first if connected
    if connection.status == "connected":
        await connection.disconnect()
    
    # Reconnect
    success = await connection.connect()
    if success:
        return {"message": f"Server {server_name} reconnected successfully"}
    else:
        raise HTTPException(status_code=500, detail=f"Failed to reconnect to server {server_name}: {connection.error}")

# Debug endpoint to check connection states
@app.get("/debug/connections")
async def debug_connections():
    """Debug endpoint to check connection states"""
    debug_info = {}
    for name, connection in mcp_manager.connections.items():
        debug_info[name] = {
            "status": connection.status,
            "error": connection.error,
            "tools_count": len(connection.tools),
            "session_exists": connection.session is not None,
            # replaced non-existent stdio_context with exit_stack presence
            "exit_stack_exists": connection.exit_stack is not None,
            "tools": [tool["name"] for tool in connection.tools]
        }
    return debug_info

# Optimization status endpoint
@app.get("/optimization-status")
async def get_optimization_status():
    """Get status of implemented optimizations"""
    return {
        "optimizations_implemented": {
            "async_exit_stack": {
                "status": "active",
                "description": "Exception-safe MCP connection management"
            },
            "parallel_tool_execution": {
                "status": "active",
                "description": "Concurrent tool execution with asyncio.gather"
            },
            "json_schema_validation": {
                "status": "active" if JSONSCHEMA_AVAILABLE else "fallback",
                "description": "Enhanced tool input validation"
            },
            "optimized_tool_registry": {
                "status": "active",
                "description": "O(1) tool lookup with global registry",
                "tools_cached": len(mcp_manager.global_tool_registry)
            },
            "telemetry_tracking": {
                "status": "active",
                "description": "Performance monitoring and metrics collection"
            },
            "enhanced_bedrock_loop": {
                "status": "active",
                "description": "Improved conversation handling with usage/metrics capture"
            }
        },
        "performance_features": {
            "connection_management": "AsyncExitStack",
            "tool_execution": "Parallel with asyncio.gather",
            "tool_lookup": "O(1) cached registry",
            "validation": "JSON Schema with fallback",
            "monitoring": "Comprehensive telemetry"
        }
    }

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    return {
        "session_id": session_id,
        "message_count": 0,
        "total_tools_used": 0,
        "history": [],
        "note": "Session history requires enhanced mode. Use main_enhanced.py for full session management."
    }

@app.get("/telemetry")
async def get_telemetry():
    """Get telemetry and performance metrics"""
    return {
        "telemetry": telemetry.get_summary(),
        "server_status": {
            server_name: {
                "status": connection.status,
                "tools_count": len(connection.tool_registry),
                "error": connection.error
            }
            for server_name, connection in mcp_manager.connections.items()
        },
        "optimization_features": {
            "async_exit_stack": True,
            "parallel_tool_execution": True,
            "json_schema_validation": JSONSCHEMA_AVAILABLE,
            "global_tool_registry": len(mcp_manager.global_tool_registry),
            "telemetry_tracking": True
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
